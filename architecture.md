# FormForge - Generador de Plantillas HTML con IA

## Descripción General
App Flutter para generar plantillas de reportes HTML a partir de estructuras JSON de formulario, usando inteligencia artificial (Firebase Gemini) para web y escritorio.

## Arquitectura MVP

### Características Principales
1. **Entrada de JSON** - Cargar archivo JSON o pegar contenido directamente
2. **Generación con IA** - Integración con Firebase/Gemini para generar plantillas HTML
3. **Visualizador HTML** - Vista previa del HTML generado
4. **Exportación** - Copiar código HTML o guardar archivo

### Estructura de Archivos (8-10 archivos total)

#### Core Files
- `lib/main.dart` - App principal y configuración
- `lib/theme.dart` - Tema visual (ya existente)

#### Screens
- `lib/screens/home_screen.dart` - Pantalla principal de la aplicación

#### Components
- `lib/widgets/json_input_widget.dart` - Widget para entrada de JSON
- `lib/widgets/html_viewer_widget.dart` - Visualizador de HTML
- `lib/widgets/file_loader_widget.dart` - Componente para cargar archivos

#### Models & Services
- `lib/models/form_structure.dart` - Modelo para estructura del formulario
- `lib/services/ai_service.dart` - Servicio de IA (Firebase/Gemini)
- `lib/services/file_service.dart` - Servicio para manejo de archivos
- `lib/utils/html_generator.dart` - Utilidades para generación HTML

### Dependencias Necesarias
- `file_picker` - Para seleccionar archivos JSON
- `flutter_html` - Para renderizar HTML
- `http` - Para llamadas HTTP a Firebase
- `firebase_core` - Para inicialización Firebase
- `cloud_functions` - Para llamadas a Cloud Functions

### Flujo de Usuario
1. Usuario carga JSON (archivo o texto directo)
2. Valida estructura JSON
3. Usuario presiona "Generar Plantilla"
4. Llamada a Firebase/Gemini con el JSON
5. Recibe plantilla HTML generada
6. Muestra vista previa HTML
7. Usuario puede copiar código o guardar archivo

### Estados de la Aplicación
- **Idle** - Esperando entrada de usuario
- **Loading** - Procesando JSON o generando HTML
- **Success** - HTML generado exitosamente
- **Error** - Error en validación o generación

### Interfaz de Usuario
- Layout responsivo para web/desktop
- Panel izquierdo: Entrada JSON y controles
- Panel derecho: Vista previa HTML
- Botón prominente "Generar Plantilla"
- Botones secundarios "Copiar" y "Guardar"