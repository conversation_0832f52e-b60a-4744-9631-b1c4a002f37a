// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for android - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDK6paoZEnAn0zN80CQH04F74VhNaDfgBQ',
    appId: '1:690664140824:web:9efd25e5a329bf353cd36d',
    messagingSenderId: '690664140824',
    projectId: 'bitacora-657e2',
    authDomain: 'bitacora-657e2.firebaseapp.com',
    databaseURL: 'https://bitacora-657e2.firebaseio.com',
    storageBucket: 'bitacora-657e2.appspot.com',
    measurementId: 'G-T6KXE1T407',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDwB6fKiWfwqsgRMQkJRnu4vE8VLYNSEtM',
    appId: '1:690664140824:ios:e15d5e9b60318a963cd36d',
    messagingSenderId: '690664140824',
    projectId: 'bitacora-657e2',
    databaseURL: 'https://bitacora-657e2.firebaseio.com',
    storageBucket: 'bitacora-657e2.appspot.com',
    androidClientId: '690664140824-ti2858j0kmn5ao8o15073ks5qr427s5k.apps.googleusercontent.com',
    iosBundleId: 'com.bitacora.template-generator',
  );
}
