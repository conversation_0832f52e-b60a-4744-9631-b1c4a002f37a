import 'dart:io';
import 'package:bitacora_template_generator/services/ai_service.dart';
import 'package:bitacora_template_generator/widgets/three_column_layout_widget.dart';
import 'package:bitacora_template_generator/widgets/auth_status_widget.dart';
import 'package:flutter/material.dart';

enum GenerationState {
  promptInput,
  generatingJson,
  jsonGenerated,
  generatingHtml,
  htmlGenerated,
  error,
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  String? _currentJsonTemplate;
  String? _currentHtmlTemplate;
  GenerationState _state = GenerationState.promptInput;
  late AnimationController _buttonAnimationController;

  @override
  void initState() {
    super.initState();
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    // _state = GenerationState.generatingJson;
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    super.dispose();
  }

  Future<void> _onPromptSubmitted(String prompt, File? attachedFile) async {
    setState(() {
      _state = GenerationState.generatingJson;
    });

    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    try {
      final jsonTemplate =
          await AiService.generateJsonFromPrompt(prompt, attachedFile);

      if (mounted) {
        if (jsonTemplate != null && jsonTemplate.isNotEmpty) {
          setState(() {
            _currentJsonTemplate = jsonTemplate;
            _currentHtmlTemplate = null; // Reset HTML when new JSON is generated
            _state = GenerationState.jsonGenerated;
          });
        } else {
          setState(() {
            _state = GenerationState.error;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
        });
      }
    }
  }

  Future<void> _onGenerateHtml() async {
    if (_currentJsonTemplate == null) return;

    setState(() {
      _state = GenerationState.generatingHtml;
    });

    try {
      final htmlTemplate = await AiService.generateHtmlTemplate(_currentJsonTemplate!);

      if (mounted) {
        if (htmlTemplate.isNotEmpty) {
          setState(() {
            _currentHtmlTemplate = htmlTemplate;
            _state = GenerationState.htmlGenerated;
          });
        } else {
          setState(() {
            _state = GenerationState.error;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 30.0, top: 8.0),
            child: AuthStatusWidget(),
          ),
          Expanded(
            child: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 8.0,
                  ),
                  child: _buildLayout(theme),
                ),
            
                // Floating loading overlay
                if (_state == GenerationState.generatingJson)
                  _buildFloatingLoadingOverlay(theme, 'Generando JSON...', 'La IA está analizando tu descripción'),
                if (_state == GenerationState.generatingHtml)
                  _buildFloatingLoadingOverlay(theme, 'Generando HTML...', 'Creando la plantilla HTML desde el JSON'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLayout(ThemeData theme) {
    return Stack(
      children: [
        // Layout principal
        ThreeColumnLayoutWidget(
          onPromptSubmitted: _onPromptSubmitted,
          jsonTemplate: _currentJsonTemplate,
          htmlTemplate: _currentHtmlTemplate,
          onGenerateHtml: _onGenerateHtml,
          showGenerateHtmlButton: _state == GenerationState.jsonGenerated && _currentHtmlTemplate == null,
          onClear: () {
            setState(() {
              _currentJsonTemplate = null;
              _currentHtmlTemplate = null;
              _state = GenerationState.promptInput;
            });
          },
        ),
      ],
    );
  }

  Widget _buildFloatingLoadingOverlay(ThemeData theme, String title, String subtitle) {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: Center(
        child: Card(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(32),
            constraints: const BoxConstraints(maxWidth: 300),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 48,
                  height: 48,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
