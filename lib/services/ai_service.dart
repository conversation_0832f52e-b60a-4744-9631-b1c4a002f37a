import 'dart:developer';
import 'dart:io';
import 'package:firebase_ai/firebase_ai.dart';

class AiService {
  static late final GenerativeModel _model;

  static void initialize() {
    // Initialize using Firebase AI with Vertex AI backend
    final vertexInstance = FirebaseAI.vertexAI();
    _model = vertexInstance.generativeModel(model: 'gemini-2.5-flash');
  }

  static Future<String?> generateJsonFromPrompt(
    String prompt,
    File? attachedFile,
  ) async {
    try {
      // Build prompt for JSON generation
      final aiPrompt = _buildJsonSystemPrompt(prompt);
      final response = await _model.generateContent(aiPrompt);

      if (response.text != null && response.text!.isNotEmpty) {
        // Extract JSON from response
        final jsonString = _extractJsonFromResponse(response.text!);

        return jsonString;
      }

      return null;
    } catch (e) {
      log('Error generating JSON with Firebase AI: $e');
      return null;
    }
  }

  /// FECHA Y HORA NO SON NECESARIOS.
  static List<Content> _buildJsonSystemPrompt(
    String userPrompt,
  ) {
    return [
      Content.text(
          '''Eres un generador experto de plantillas JSON para formularios. Tu salida SIEMPRE debe ser un único JSON válido y bien formado, siguiendo estrictamente la estructura definida abajo. No incluyas explicaciones, comentarios ni texto fuera del JSON.

Estructura obligatoria:

1. Template:
   - name (string, deducido del caso de uso)
   - groups (array)

2. Group:
   - name (string, deducido de la temática)
   - order (entero, secuencia lógica)
   - blocks (array)
   - conditions ([] por defecto)

3. Block:
   - content (string, "" por defecto)
   - row (entero, disposición vertical)
   - order (entero, orden horizontal dentro de la fila)
   - weight (entero, ancho relativo, 1 por defecto)
   - role (null por defecto)
   - field (objeto)
   - options (objeto)
   - conditions ([] por defecto)

4. field:
   - custom_field_type (uno de: TEXT, TEXT_AREA, INTEGER, FLOAT, DATE_TIME, DATE, TIME, RADIO, SELECT, CHECKBOX, PROJECT, USER, CHECKBOX_GROUP)
   - name (string, deducido del caso de uso)
   - organization_id (entero)
   - parent_id (null por defecto)
   - allowed_values (array vacío o con opciones si SELECT, RADIO o CHECKBOX_GROUP)

5. allowed_values:
   - label (string)
   - value (string)
 
6. options:
   - is_required (booleano, deducido de si es obligatorio)
   - placeholder (string, "" por defecto)

7. Reglas:
   - Interpretar libremente la descripción del usuario y deducir grupos y campos.
   - Ordenar el formulario de manera lógica.
   - Asignar tipos de campo correctos según el dato descrito.
   - Usar timestamps actuales en UTC.
   - Si el usuario no menciona un dato, no inventarlo salvo que sea necesario para cumplir la estructura.
   - La respuesta debe contener SOLO el JSON, sin explicaciones.

Input del usuario: Descripción libre de su caso de uso, por ejemplo:
"Quiero un formulario para registrar incidentes en obra, con datos del trabajador, fecha, hora, descripción y gravedad."

Output esperado: JSON válido y bien formado según la estructura, sin el key de Template, directamente el objeto.'''),
      Content.text(userPrompt)
    ];
  }

  static Future<String> generateHtmlTemplate(String formStructure) async {
    try {
      // Generate HTML template using Firebase AI (Gemini)
      final aiPrompt = _buildHTMLPrompt(formStructure);
      final response = await _model.generateContent(aiPrompt);

      if (response.text != null && response.text!.isNotEmpty) {
        // Extract JSON from response
        String htmlCode = _extractHtmlFromResponse(response.text!);
        return htmlCode.isNotEmpty
            ? htmlCode
            : _getFallbackTemplate(formStructure);
      }

      return _getFallbackTemplate(formStructure);
    } catch (e) {
      // Fallback to mock generation if Firebase AI is not available
      return _getMockHtmlTemplate(formStructure);
    }
  }

  static String _extractJsonFromResponse(String response) {
    // Extract JSON from AI response
    // Look for JSON content between ```json and ``` or { and }

    // First try to find JSON block in markdown format
    final jsonBlockRegex =
        RegExp(r'```json\s*([\s\S]*?)\s*```', caseSensitive: false);
    final match = jsonBlockRegex.firstMatch(response);

    if (match != null && match.group(1) != null) {
      return match.group(1)!.trim();
    }

    // If no markdown block, look for JSON object
    final jsonObjectRegex = RegExp(r'\{[\s\S]*\}');
    final objMatch = jsonObjectRegex.firstMatch(response);

    if (objMatch != null) {
      return objMatch.group(0)!.trim();
    }

    return '';
  }

  static String _extractHtmlFromResponse(String response) {
    // Extract HTML code from Gemini response
    // Look for HTML content between ```html and ``` or <!DOCTYPE html> and </html>

    // First try to find HTML block in markdown format
    final htmlBlockRegex =
        RegExp(r'```html\s*([\s\S]*?)\s*```', caseSensitive: false);
    final match = htmlBlockRegex.firstMatch(response);

    if (match != null && match.group(1) != null) {
      return match.group(1)!.trim();
    }

    // If no markdown block, look for HTML document
    final htmlDocRegex =
        RegExp(r'<!DOCTYPE html[\s\S]*?</html>', caseSensitive: false);
    final docMatch = htmlDocRegex.firstMatch(response);

    if (docMatch != null) {
      return docMatch.group(0)!.trim();
    }

    // If still nothing, return the response as is (might be plain HTML)
    if (response.toLowerCase().contains('<html') &&
        response.toLowerCase().contains('</html>')) {
      return response.trim();
    }

    return '';
  }

  static List<Content> _buildHTMLPrompt(String formStructure) {
    return [
      Content.text('''
Genera una plantilla de HTML para un reporte, optimizada para su conversión a PDF en formato A4. La plantilla HTML debe generarse a partir de una estructura JSON que define los grupos, bloques y campos de un formulario.

## Objetivo

Crear un HTML limpio, compacto, centrado y profesional, tanto para visualización en pantalla como para impresión en PDF. La plantilla debe estar preparada para inyección de datos mediante Mustache y **debe usar únicamente CSS2**.

## Instrucciones clave

### Maquetado y estilo visual

- **Usar únicamente CSS2**: No utilizar propiedades de CSS3 como `grid`, `flexbox`, `transform`, `box-shadow`, etc. Usar solo propiedades compatibles con CSS2.
- Usar una fuente sans-serif legible (como Arial o Roboto) con **tamaño reducido (por ejemplo, 11px o menor si es legible)**.
- Minimizar al máximo el uso de espacio vertical:
  - Márgenes y paddings internos deben ser **muy reducidos o nulos**.
  - El diseño debe ser **compacto, sin espacio innecesario entre secciones**.
- Para impresión en PDF (A4), usar `@media print` para:
  - Eliminar márgenes y paddings innecesarios.
  - Optimizar el uso del espacio en la hoja.
- Usar estilo minimalista pero con un color primario:
  - **No usar sombras ni efectos CSS3**
  - Que se vea como un reporte profesional hecho por un experto analista de datos.
- Usar tablas para representar los campos como pares etiqueta–valor alineados horizontalmente, **compactando filas y columnas para ahorrar espacio** siempre que sea posible.
- Las miniaturas de las imágenes con border redondeados, en estilo cover, cuadradas.

### Estructura del HTML

- Incluir `<!DOCTYPE html>`, así como las secciones completas de `<head>`, `<style>` y `<body>`.
- **IMPORTANTE**: Usar `{{#entries}} ... {{/entries}}` para soportar la iteración de los registros.
- Mostrar el título principal del reporte usando `{{template_name}}`. Esto no se debe repetir para cada registro, solo al inicio del reporte.
- Cada registro (entry) debe tener un encabezado con su fecha (day) y hora (time) y como subencabezado el nombre de su autor (creator_name). 
- Cada `group` que tenga un `name` debe representarse como una sección con un encabezado (`<h2>`).
- Cada campo (`custom_field`) debe representarse como un par etiqueta–valor:
  - La etiqueta será el `name` del campo.
  - El valor será un placeholder Mustache con el `id` de custom_field, por ejemplo: `{{168}}`.
  - Solo muestralo si es que tiene datos, si esta vacío o el valor es null no muestres el campo.
- Los grupos solo deben mostrarse si tienen campos hijos con datos, sino no debe mostrarse, nisiquiera el titulo o nombre del grupo.
- Obvia todo tipo de referencia a grupos y campos condicionales, solo muestra si hay datos.
- En la parte inferior de cada registro debemos de colocar los attachments como una galería de miniaturas compacta.

### Galería de Attachments

Al final de cada registro incluir esta galería compacta de attachments:

```html
{{#has_attachments}}
<div class="attachments-gallery">
    <h2 class="attachments-title">ARCHIVOS ADJUNTOS</h2>
    <div class="attachments-grid">
        {{#attachments}}
        <a href="{{download_url}}" target="_blank" class="attachment-item">
            {{#isImage}}
            <img src="{{url}}" alt="Attachment" class="attachment-image" />
            {{/isImage}}
            {{#isAudio}}
            <svg class="attachment-icon" viewBox="0 0 24 24">
                <path d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18.01,19.86 21,16.28 21,12C21,7.72 18.01,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z" />
            </svg>
            {{/isAudio}}
            {{#isFile}}
            <svg class="attachment-icon" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            {{/isFile}}
        </a>
        {{/attachments}}
    </div>
</div>
{{/has_attachments}}
```

CSS para la galería usando **solo CSS2**:

```css
.attachments-gallery { margin-top: 12px; padding-top: 8px; }
.attachments-title { margin-bottom: 8px; }
.attachments-grid {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    left: 0;
    right: 0;
}
.attachments-grid:after {
    content: "";
    display: table;
    clear: both;
}
.attachment-item {
    position: relative;
    float: left;
    width: 22.82%;
    margin: 4px 1%;
    height: 100px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: #f8f9fa;
    text-align: center;
    text-decoration: none;
    color: inherit;
    display: block;
}
.attachment-image { width: 100%; height: 100%; }
.attachment-icon { width: 20px; height: 20px; fill: #666; }
@media print { 
    .attachments-gallery { margin-top: 6px; }
    .attachments-grid { overflow: hidden; }
    .attachment-item { 
        width: 18%;
        margin: 2px 1%;
        height: 80px;
        page-break-inside: avoid;
    }
    .attachment-icon { width: 16px; height: 16px; }
}
```

### Múltiples registros

* El reporte está pensado para mostrar un solo registro por página, si son muy pocos datos, podemos colocar 2 registros por página siempre y cuando quepan en una sola página. No queremos que los datos se dividan entre páginas.
* Se asume que el motor de generación replicará la plantilla por cada registro en páginas independientes del PDF.
* El diseño debe ser lo suficientemente compacto para que idealmente cada registro quepa en una hoja A4.
* **IMPORTANTE**: Usar `{{#entries}} ... {{/entries}}` para iterar sobre todos los registros.

## Uso de Mustache

Los datos deben inyectarse a partir una estructura de datos con el siguiente formato:

```json
{
    "template_name": "Nombre de plantilla",
    "entries": [
        {
            "group_id": {
                "custom_field_id": "value",
                "custom_field_id": "value",
                ...
            },
            "group_id": {
                "custom_field_id": "value",
                ...
            },
            "day": "2025/03/29",
            "time": "15:29",
            "creator_name": "Marco Pedraza",
            "has_attachments": true,
            "attachments": [
                {"url": "https://image.com/dssd", "download_url": "https://image.com/dssd", "isImage": true, "isAudio": false, "isFile": false},
                {"url": "https://aws.com/document.pdf", "download_url": "https://aws.com/document.pdf", "isImage": false, "isAudio": false, "isFile": true}
            ],
            ...
        },
        ...
    ]
}
```

Ejemplo de uso de Mustache:

```mustache
<td>{{123}}</td>
```

## Resultado esperado

Un archivo HTML completo, limpio y legible, optimizado para conversión a PDF A4, **usando únicamente CSS2**, con estructura preparada para inyección de datos con Mustache y lógica condicional para los grupos correspondientes. La galería de attachments debe estar integrada al final de cada registro de forma compacta y profesional. **El HTML debe incluir obligatoriamente `{{#entries}} ... {{/entries}}` para iterar sobre los registros**.

Devuelve solo el contenido HTML generado, sin explicaciones, comentarios ni encabezados adicionales. No escribas ninguna palabra antes o después del HTML. Solo el HTML puro como texto.
'''),
      Content.text(formStructure)
    ];
  }

  static String _getFallbackTemplate(String formStructure) {
    return '''
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 700; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .report-meta { background: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table-container { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th { background: #6366f1; color: white; padding: 15px; text-align: left; font-weight: 600; }
        td { padding: 15px; border-bottom: 1px solid #e5e7eb; }
        tr:hover { background: #f8fafc; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; color: #6b7280; }
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header h1 { font-size: 2rem; }
            th, td { padding: 10px 8px; font-size: 14px; }
        }
        @media print {
            body { background: white; }
            .container { max-width: none; }
            .header { background: #6366f1 !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>title</h1>
            <p>description</p>
        </div>
        
        <div class="report-meta">
            <p><strong>Fecha de generación:</strong> <span id="currentDate"></span></p>
            <p><strong>Total de registros:</strong> <span id="recordCount">0</span></p>
        </div>
        
   
        
        <div class="footer">
            <p>Reporte generado automáticamente • Bitacora AI</p>
        </div>
    </div>
    
    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // Función para agregar datos dinámicamente
      
        function updateRecordCount() {
            const count = document.querySelectorAll('#dataRows tr').length;
            document.getElementById('recordCount').textContent = count;
        }
        
        // Ejemplo de uso:
       
    </script>
</body>
</html>''';
  }

  static String _getMockHtmlTemplate(String formStructure) {
    // Return the same fallback template for now
    // In a real implementation, this could be a different mock template
    return _getFallbackTemplate(formStructure);
  }
}
