import 'dart:convert';
import 'package:bitacora_template_generator/services/file_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class GeneratedJsonDisplayWidget extends StatefulWidget {
  final String jsonString;
  final VoidCallback onClear;
  final Function(String)? onJsonUpdated;

  const GeneratedJsonDisplayWidget({
    super.key,
    required this.jsonString,
    required this.onClear,
    this.onJsonUpdated,
  });

  @override
  State<GeneratedJsonDisplayWidget> createState() =>
      _GeneratedJsonDisplayWidgetState();
}

class _GeneratedJsonDisplayWidgetState
    extends State<GeneratedJsonDisplayWidget> {
  String? _successMessage;
  String? _errorMessage;
  bool _isSavingToServer = false;

  String get _formattedJson {
    try {
      // First, clean up the JSON string
      String cleanJsonString = _cleanJsonString(widget.jsonString);

      // Try to parse and reformat the JSON
      final jsonObject = jsonDecode(cleanJsonString);
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(jsonObject);
    } catch (e) {
      // If parsing fails, return the cleaned string
      return _cleanJsonString(widget.jsonString);
    }
  }

  String _cleanJsonString(String jsonString) {
    String cleaned = jsonString;

    // Handle different types of escaped characters
    cleaned = cleaned.replaceAll('\\n', '\n');
    cleaned = cleaned.replaceAll('\\t', '\t');
    cleaned = cleaned.replaceAll('\\r', '\r');
    cleaned = cleaned.replaceAll('\\"', '"');
    cleaned = cleaned.replaceAll('\\\\', '\\');

    // Remove any markdown code block markers if present
    cleaned = cleaned.replaceAll(RegExp(r'```json\s*'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\s*```'), '');

    // Trim whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  String get _jsonTitle {
    try {
      // Clean the JSON string first
      String cleanJsonString = widget.jsonString;
      if (cleanJsonString.contains('\\n')) {
        cleanJsonString = cleanJsonString.replaceAll('\\n', '\n');
      }
      if (cleanJsonString.contains('\\"')) {
        cleanJsonString = cleanJsonString.replaceAll('\\"', '"');
      }

      final jsonObject = jsonDecode(cleanJsonString);
      return jsonObject['title'] ?? 'Plantilla JSON';
    } catch (e) {
      return 'Plantilla JSON';
    }
  }

  Future<void> _copyToClipboard() async {
    try {
      await Clipboard.setData(ClipboardData(text: _formattedJson));
      setState(() {
        _successMessage = 'JSON copiado al portapapeles';
        _errorMessage = null;
      });

      // Clear message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _successMessage = null;
          });
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error al copiar: ${e.toString()}';
        _successMessage = null;
      });
    }
  }

  Future<void> _saveAsFile() async {
    try {
      final fileName = _jsonTitle
          .toLowerCase()
          .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
          .replaceAll(RegExp(r'\s+'), '_');

      final success = await FileService.saveJsonFile(_formattedJson, fileName);

      if (success) {
        setState(() {
          _successMessage = 'Archivo JSON guardado exitosamente';
          _errorMessage = null;
        });

        // Clear message after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _successMessage = null;
            });
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error al guardar archivo: ${e.toString()}';
        _successMessage = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'JSON Generado Exitosamente',
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: theme.colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            _jsonTitle,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action buttons
                Column(
                  children: [
                    // Primera fila de botones
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _copyToClipboard,
                            icon: Icon(
                              Icons.copy,
                              size: 18,
                              color: theme.colorScheme.primary,
                            ),
                            label: Text(
                              'Copiar',
                              style: TextStyle(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: theme.colorScheme.outline),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _saveAsFile,
                            icon: Icon(
                              Icons.save,
                              size: 18,
                              color: theme.colorScheme.primary,
                            ),
                            label: Text(
                              'Guardar',
                              style: TextStyle(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: theme.colorScheme.outline),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        OutlinedButton.icon(
                          onPressed: widget.onClear,
                          icon: Icon(
                            Icons.clear,
                            size: 18,
                            color: theme.colorScheme.error,
                          ),
                          label: Text(
                            'Limpiar',
                            style: TextStyle(
                              color: theme.colorScheme.error,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: theme.colorScheme.error),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Segunda fila - Botón de guardar en servidor
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isSavingToServer ? null : _saveToServer,
                        icon: _isSavingToServer
                            ? SizedBox(
                                width: 18,
                                height: 18,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    theme.colorScheme.onPrimary,
                                  ),
                                ),
                              )
                            : Icon(
                                Icons.cloud_upload,
                                size: 18,
                              ),
                        label: Text(
                          _isSavingToServer ? 'Guardando...' : 'Guardar en Servidor',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),

                // Success/Error messages
                if (_successMessage != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: Colors.green,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _successMessage!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.green.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                if (_errorMessage != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.errorContainer
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.error.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: theme.colorScheme.error,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onErrorContainer,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),

          // JSON Content (expandable)
          Expanded(
            child: Container(
              margin: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: SelectableText(
                  _formattedJson,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                    color: theme.colorScheme.onSurface,
                    height: 1.4,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveToServer() async {
    setState(() {
      _isSavingToServer = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final response = await TemplateService.saveTemplate(_formattedJson);

      if (response != null) {
        // Convertir la respuesta del servidor a JSON string formateado
        const encoder = JsonEncoder.withIndent('  ');
        final updatedJsonString = encoder.convert(response);

        setState(() {
          _successMessage = 'Plantilla guardada en el servidor exitosamente';
          _isSavingToServer = false;
        });

        // Notificar al widget padre que el JSON se ha actualizado
        if (widget.onJsonUpdated != null) {
          widget.onJsonUpdated!(updatedJsonString);
        }

        // Clear success message after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _successMessage = null;
            });
          }
        });
      } else {
        setState(() {
          _errorMessage = 'Error al guardar en el servidor';
          _isSavingToServer = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error al guardar en servidor: $e';
        _isSavingToServer = false;
      });
    }
  }
}
