import 'package:flutter/material.dart';
import 'package:bitacora_template_generator/services/auth_service.dart';

class AuthStatusWidget extends StatefulWidget {
  const AuthStatusWidget({super.key});

  @override
  State<AuthStatusWidget> createState() => _AuthStatusWidgetState();
}

class _AuthStatusWidgetState extends State<AuthStatusWidget> {
  bool _isLoading = false;
  bool _showToken = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Indicador de estado
          Icon(
            AuthService.isAuthenticated ? Icons.verified : Icons.error_outline,
            size: 12,
            color: AuthService.isAuthenticated ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 6),

          // Estado de autenticación
          Text(
            AuthService.isAuthenticated ? 'Autenticado' : 'Sin autenticar',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color:
                      AuthService.isAuthenticated ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                  fontSize: 10,
                ),
          ),

          if (AuthService.isAuthenticated) ...[
            const SizedBox(width: 8),
            // Token (oculto por defecto, clickeable)
            GestureDetector(
              onTap: _toggleTokenVisibility,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(25),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showToken ? Icons.visibility_off : Icons.visibility,
                      size: 10,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _showToken
                          ? AuthService.sessionToken ?? 'N/A'
                          : '••••••••',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'monospace',
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
          IconButton(
              onPressed: _handleRefresh,
              icon: Icon(
                Icons.refresh,
                size: 16,
              )),
          // Indicador de carga
          if (_isLoading) ...[
            const SizedBox(width: 4),
            SizedBox(
              width: 10,
              height: 10,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _toggleTokenVisibility() {
    setState(() {
      _showToken = !_showToken;
    });
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await AuthService.refreshAuthIfNeeded();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar(
          success ? 'Token actualizado' : 'Error al actualizar token',
          success ? Colors.green : Colors.red,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar('Error: $e', Colors.red);
      }
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
      ),
    );
  }
}
