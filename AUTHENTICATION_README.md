# Sistema de Autenticación - Bitacora.io

## Descripción

Se ha implementado un sistema de autenticación automática que se conecta a la API de Bitacora.io para obtener un token de sesión que puede ser utilizado en otras peticiones HTTP.

## Características Implementadas

### 1. Servicio de Autenticación (`lib/services/auth_service.dart`)

- **Autenticación automática**: Se ejecuta al iniciar la aplicación
- **Credenciales por defecto**: 
  - Email: `<EMAIL>`
  - Password: `12345678`
- **Gestión de token**: Almacena y proporciona el `session_token`
- **Métodos principales**:
  - `login()`: Autenticación con credenciales por defecto
  - `loginWithCredentials(email, password)`: Autenticación con credenciales específicas
  - `logout()`: Cierra la sesión actual
  - `getAuthHeaders()`: Obtiene headers de autorización para peticiones
  - `refreshAuthIfNeeded()`: Reintenta autenticación si es necesario

### 2. Widget de Estado de Autenticación (`lib/widgets/auth_status_widget.dart`)

- **Indicador visual**: Muestra el estado actual de autenticación
- **Controles interactivos**: Botones para autenticar, verificar y cerrar sesión
- **Información del token**: Muestra una vista parcial del token actual
- **Feedback al usuario**: Mensajes de éxito/error mediante SnackBars

### 3. Integración en la Aplicación

- **Inicialización automática**: Se ejecuta en `main.dart` al arrancar la app
- **Interfaz integrada**: Widget de estado visible en la pantalla principal
- **Ejemplo de uso**: Método en `AiService` que muestra cómo hacer peticiones autenticadas

## Cómo Usar el Sistema

### Verificar Estado de Autenticación

```dart
// Verificar si hay una sesión activa
if (AuthService.isAuthenticated) {
  print('Usuario autenticado');
  print('Token: ${AuthService.sessionToken}');
}
```

### Hacer Peticiones Autenticadas

```dart
// Obtener headers con autorización
final headers = AuthService.getAuthHeaders();

// Hacer petición HTTP
final response = await http.get(
  Uri.parse('https://api.bitacora.io/api/tu-endpoint'),
  headers: headers,
);
```

### Manejar Expiración de Token

```dart
// Verificar y renovar autenticación si es necesario
final isValid = await AuthService.refreshAuthIfNeeded();
if (isValid) {
  // Proceder con la petición
} else {
  // Manejar error de autenticación
}
```

## Configuración

### Cambiar Credenciales por Defecto

Si necesitas cambiar las credenciales por defecto, modifica las constantes en `auth_service.dart`:

```dart
static const String _defaultEmail = '<EMAIL>';
static const String _defaultPassword = 'tu-password';
```

### Cambiar URL de la API

Para cambiar la URL base de la API, modifica la constante:

```dart
static const String _baseUrl = 'https://tu-api.com/api';
```

## Estructura de Respuesta Esperada

El servicio espera que la API de login retorne un JSON con el token de sesión:

```json
{
  "session_token": "tu-token-aqui"
}
```

O alternativamente:

```json
{
  "data": {
    "session_token": "tu-token-aqui"
  }
}
```

## Manejo de Errores

El sistema incluye manejo robusto de errores:

- **Errores de red**: Se capturan y registran en los logs
- **Respuestas inválidas**: Se valida la estructura de la respuesta
- **Token faltante**: Se detecta cuando no se encuentra el token en la respuesta
- **Feedback visual**: Los errores se muestran al usuario mediante SnackBars

## Logs y Debugging

El sistema utiliza `dart:developer` para logging. Para ver los logs:

1. Ejecuta la aplicación en modo debug
2. Abre la consola de Flutter
3. Busca mensajes que comiencen con:
   - "Intentando autenticación con:"
   - "Respuesta de autenticación - Status:"
   - "Autenticación exitosa"
   - "Error en autenticación:"

## Próximos Pasos

Para extender el sistema de autenticación:

1. **Persistencia**: Guardar el token en almacenamiento local
2. **Renovación automática**: Implementar renovación automática del token
3. **Múltiples usuarios**: Soporte para diferentes usuarios
4. **Configuración**: Interfaz para cambiar credenciales desde la app

## Ejemplo de Uso Completo

```dart
// En tu servicio o widget
class MiServicio {
  static Future<Map<String, dynamic>?> obtenerDatos() async {
    // Verificar autenticación
    if (!AuthService.isAuthenticated) {
      final success = await AuthService.login();
      if (!success) return null;
    }
    
    // Hacer petición autenticada
    final headers = AuthService.getAuthHeaders();
    final response = await http.get(
      Uri.parse('https://api.bitacora.io/api/mis-datos'),
      headers: headers,
    );
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    
    return null;
  }
}
```
